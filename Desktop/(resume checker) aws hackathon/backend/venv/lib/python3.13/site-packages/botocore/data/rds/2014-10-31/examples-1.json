{"version": "1.0", "examples": {"AddSourceIdentifierToSubscription": [{"input": {"SourceIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SubscriptionName": "mymysqleventsubscription"}, "output": {"EventSubscription": {}}, "comments": {"input": {}, "output": {}}, "description": "This example add a source identifier to an event notification subscription.", "id": "add-source-identifier-to-subscription-93fb6a15-0a59-4577-a7b5-e12db9752c14", "title": "To add a source identifier to an event notification subscription"}], "AddTagsToResource": [{"input": {"ResourceName": "arn:aws:rds:us-east-1:992648334831:og:mymysqloptiongroup", "Tags": [{"Key": "Staging", "Value": "LocationDB"}]}, "comments": {"input": {}, "output": {}}, "description": "This example adds a tag to an option group.", "id": "add-tags-to-resource-fa99ef50-228b-449d-b893-ca4d4e9768ab", "title": "To add tags to a resource"}], "ApplyPendingMaintenanceAction": [{"input": {"ApplyAction": "system-update", "OptInType": "immediate", "ResourceIdentifier": "arn:aws:rds:us-east-1:992648334831:db:my<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "output": {"ResourcePendingMaintenanceActions": {}}, "comments": {"input": {}, "output": {}}, "description": "This example immediately applies a pending system update to a DB instance.", "id": "apply-pending-maintenance-action-2a026047-8bbb-47fc-b695-abad9f308c24", "title": "To apply a pending maintenance action"}], "AuthorizeDBSecurityGroupIngress": [{"input": {"CIDRIP": "***********/32", "DBSecurityGroupName": "mydbsecuritygroup"}, "output": {"DBSecurityGroup": {}}, "comments": {"input": {}, "output": {}}, "description": "This example authorizes access to the specified security group by the specified CIDR block.", "id": "authorize-db-security-group-ingress-ebf9ab91-8912-4b07-a32e-ca150668164f", "title": "To authorize DB security group integress"}], "CopyDBClusterParameterGroup": [{"input": {"SourceDBClusterParameterGroupIdentifier": "mydbclusterparametergroup", "TargetDBClusterParameterGroupDescription": "My DB cluster parameter group copy", "TargetDBClusterParameterGroupIdentifier": "mydbclusterparametergroup-copy"}, "output": {"DBClusterParameterGroup": {}}, "comments": {"input": {}, "output": {}}, "description": "This example copies a DB cluster parameter group.", "id": "copy-db-cluster-parameter-group-6fefaffe-cde9-4dba-9f0b-d3f593572fe4", "title": "To copy a DB cluster parameter group"}], "CopyDBClusterSnapshot": [{"input": {"SourceDBClusterSnapshotIdentifier": "rds:sample-cluster-2016-09-14-10-38", "TargetDBClusterSnapshotIdentifier": "cluster-snapshot-copy-1"}, "output": {"DBClusterSnapshot": {}}, "comments": {"input": {}, "output": {}}, "description": "The following example copies an automated snapshot of a DB cluster to a new DB cluster snapshot.", "id": "to-copy-a-db-cluster-snapshot-1473879770564", "title": "To copy a DB cluster snapshot"}], "CopyDBParameterGroup": [{"input": {"SourceDBParameterGroupIdentifier": "mymysqlparametergroup", "TargetDBParameterGroupDescription": "My MySQL parameter group copy", "TargetDBParameterGroupIdentifier": "mymysqlparametergroup-copy"}, "output": {"DBParameterGroup": {}}, "comments": {"input": {}, "output": {}}, "description": "This example copies a DB parameter group.", "id": "copy-db-parameter-group-610d4dba-2c87-467f-ae5d-edd7f8e47349", "title": "To copy a DB parameter group"}], "CopyDBSnapshot": [{"input": {"SourceDBSnapshotIdentifier": "mydbsnapshot", "TargetDBSnapshotIdentifier": "mydbsnapshot-copy"}, "output": {"DBSnapshot": {}}, "comments": {"input": {}, "output": {}}, "description": "This example copies a DB snapshot.", "id": "copy-db-snapshot-1b2f0210-bc67-415d-9822-6eecf447dc86", "title": "To copy a DB snapshot"}], "CopyOptionGroup": [{"input": {"SourceOptionGroupIdentifier": "mymysqloptiongroup", "TargetOptionGroupDescription": "My MySQL option group copy", "TargetOptionGroupIdentifier": "mymysqloptiongroup-copy"}, "output": {"OptionGroup": {}}, "comments": {"input": {}, "output": {}}, "description": "This example copies an option group.", "id": "copy-option-group-8d5c01c3-8846-4e9c-a4b0-1b7237f7d0ec", "title": "To copy an option group"}], "CreateDBCluster": [{"input": {"AvailabilityZones": ["us-east-1a"], "BackupRetentionPeriod": 1, "DBClusterIdentifier": "mydbcluster", "DBClusterParameterGroupName": "mydbclusterparametergroup", "DatabaseName": "myaurorad<PERSON>", "Engine": "aurora", "EngineVersion": "5.6.10a", "MasterUserPassword": "mypassword", "MasterUsername": "myuser", "Port": 3306, "StorageEncrypted": true}, "output": {"DBCluster": {}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a DB cluster.", "id": "create-db-cluster-423b998d-eba9-40dd-8e19-96c5b6e5f31d", "title": "To create a DB cluster"}], "CreateDBClusterParameterGroup": [{"input": {"DBClusterParameterGroupName": "mydbclusterparametergroup", "DBParameterGroupFamily": "aurora5.6", "Description": "My DB cluster parameter group"}, "output": {"DBClusterParameterGroup": {}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a DB cluster parameter group.", "id": "create-db-cluster-parameter-group-8eb1c3ae-1965-4262-afe3-ee134c4430b1", "title": "To create a DB cluster parameter group"}], "CreateDBClusterSnapshot": [{"input": {"DBClusterIdentifier": "mydbcluster", "DBClusterSnapshotIdentifier": "mydbclustersnapshot"}, "output": {"DBClusterSnapshot": {}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a DB cluster snapshot.", "id": "create-db-cluster-snapshot-", "title": "To create a DB cluster snapshot"}], "CreateDBInstance": [{"input": {"AllocatedStorage": 5, "DBInstanceClass": "db.t2.micro", "DBInstanceIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Engine": "MySQL", "MasterUserPassword": "MyPassword", "MasterUsername": "MyUser"}, "output": {"DBInstance": {}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a DB instance.", "id": "create-db-instance-57eb5d16-8bf8-4c84-9709-1700322b37b9", "title": "To create a DB instance."}], "CreateDBInstanceReadReplica": [{"input": {"AvailabilityZone": "us-east-1a", "CopyTagsToSnapshot": true, "DBInstanceClass": "db.t2.micro", "DBInstanceIdentifier": "mydbreadreplica", "PubliclyAccessible": true, "SourceDBInstanceIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StorageType": "gp2", "Tags": [{"Key": "mydbreadreplicakey", "Value": "mydbreadreplicavalue"}]}, "output": {"DBInstance": {}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a DB instance read replica.", "id": "create-db-instance-read-replica-81b41cd5-2871-4dae-bc59-3e264449d5fe", "title": "To create a DB instance read replica."}], "CreateDBParameterGroup": [{"input": {"DBParameterGroupFamily": "mysql5.6", "DBParameterGroupName": "mymysqlparametergroup", "Description": "My MySQL parameter group"}, "output": {"DBParameterGroup": {}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a DB parameter group.", "id": "create-db-parameter-group-42afcc37-12e9-4b6a-a55c-b8a141246e87", "title": "To create a DB parameter group."}], "CreateDBSecurityGroup": [{"input": {"DBSecurityGroupDescription": "My DB security group", "DBSecurityGroupName": "mydbsecuritygroup"}, "output": {"DBSecurityGroup": {}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a DB security group.", "id": "create-db-security-group-41b6786a-539e-42a5-a645-a8bc3cf99353", "title": "To create a DB security group."}], "CreateDBSnapshot": [{"input": {"DBInstanceIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DBSnapshotIdentifier": "mydbsnapshot"}, "output": {"DBSnapshot": {}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a DB snapshot.", "id": "create-db-snapshot-e10e0e2c-9ac4-426d-9b17-6b6a3e382ce2", "title": "To create a DB snapshot."}], "CreateDBSubnetGroup": [{"input": {"DBSubnetGroupDescription": "My DB subnet group", "DBSubnetGroupName": "mydbsubnetgroup", "SubnetIds": ["subnet-1fab8a69", "subnet-d43a468c"]}, "output": {"DBSubnetGroup": {}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a DB subnet group.", "id": "create-db-subnet-group-c3d162c2-0ec4-4955-ba89-18967615fdb8", "title": "To create a DB subnet group."}], "CreateEventSubscription": [{"input": {"Enabled": true, "EventCategories": ["availability"], "SnsTopicArn": "arn:aws:sns:us-east-1:992648334831:MyDemoSNSTopic", "SourceIds": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "SourceType": "db-instance", "SubscriptionName": "mymysqleventsubscription"}, "output": {"EventSubscription": {}}, "comments": {"input": {}, "output": {}}, "description": "This example creates an event notification subscription.", "id": "create-event-subscription-00dd0ee6-0e0f-4a38-ae83-e5f2ded5f69a", "title": "To create an event notification subscription"}], "CreateOptionGroup": [{"input": {"EngineName": "MySQL", "MajorEngineVersion": "5.6", "OptionGroupDescription": "My MySQL 5.6 option group", "OptionGroupName": "mymysqloptiongroup"}, "output": {"OptionGroup": {}}, "comments": {"input": {}, "output": {}}, "description": "This example creates an option group.", "id": "create-option-group-a7708c87-1b79-4a5e-a762-21cf8fc62b78", "title": "To create an option group"}], "DeleteDBCluster": [{"input": {"DBClusterIdentifier": "mydbcluster", "SkipFinalSnapshot": true}, "output": {"DBCluster": {}}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified DB cluster.", "id": "delete-db-cluster-927fc2c8-6c67-4075-b1ba-75490be0f7d6", "title": "To delete a DB cluster."}], "DeleteDBClusterParameterGroup": [{"input": {"DBClusterParameterGroupName": "mydbclusterparametergroup"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified DB cluster parameter group.", "id": "delete-db-cluster-parameter-group-364f5555-ba0a-4cc8-979c-e769098924fc", "title": "To delete a DB cluster parameter group."}], "DeleteDBClusterSnapshot": [{"input": {"DBClusterSnapshotIdentifier": "mydbclustersnapshot"}, "output": {"DBClusterSnapshot": {}}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified DB cluster snapshot.", "id": "delete-db-cluster-snapshot-c67e0d95-670e-4fb5-af90-6d9a70a91b07", "title": "To delete a DB cluster snapshot."}], "DeleteDBInstance": [{"input": {"DBInstanceIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SkipFinalSnapshot": true}, "output": {"DBInstance": {}}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified DB instance.", "id": "delete-db-instance-4412e650-949c-488a-b32a-7d3038ebccc4", "title": "To delete a DB instance."}], "DeleteDBParameterGroup": [{"input": {"DBParameterGroupName": "mydbparamgroup3"}, "comments": {"input": {}, "output": {}}, "description": "The following example deletes a DB parameter group.", "id": "to-delete-a-db-parameter-group-1473888796509", "title": "To delete a DB parameter group"}], "DeleteDBSecurityGroup": [{"input": {"DBSecurityGroupName": "mysecgroup"}, "comments": {"input": {}, "output": {}}, "description": "The following example deletes a DB security group.", "id": "to-delete-a-db-security-group-1473960141889", "title": "To delete a DB security group"}], "DeleteDBSnapshot": [{"input": {"DBSnapshotIdentifier": "mydbsnapshot"}, "output": {"DBSnapshot": {}}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified DB snapshot.", "id": "delete-db-snapshot-505d6b4e-8ced-479c-856a-c460a33fe07b", "title": "To delete a DB cluster snapshot."}], "DeleteDBSubnetGroup": [{"input": {"DBSubnetGroupName": "mydbsubnetgroup"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified DB subnetgroup.", "id": "delete-db-subnet-group-4ae00375-511e-443d-a01d-4b9f552244aa", "title": "To delete a DB subnet group."}], "DeleteEventSubscription": [{"input": {"SubscriptionName": "myeventsubscription"}, "output": {"EventSubscription": {}}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified DB event subscription.", "id": "delete-db-event-subscription-d33567e3-1d5d-48ff-873f-0270453f4a75", "title": "To delete a DB event subscription."}], "DeleteOptionGroup": [{"input": {"OptionGroupName": "mydboptiongroup"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified option group.", "id": "delete-db-option-group-578be2be-3095-431a-9ea4-9a3c3b0daef4", "title": "To delete an option group."}], "DescribeAccountAttributes": [{"input": {}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists account attributes.", "id": "describe-account-attributes-683d3ff7-5524-421a-8da5-e88f1ea2222b", "title": "To list account attributes"}], "DescribeCertificates": [{"input": {"CertificateIdentifier": "rds-ca-2015", "MaxRecords": 20}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists up to 20 certificates for the specified certificate identifier.", "id": "describe-certificates-9d71a70d-7908-4444-b43f-321d842c62dc", "title": "To list certificates"}], "DescribeDBClusterParameterGroups": [{"input": {"DBClusterParameterGroupName": "mydbclusterparametergroup"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists settings for the specified DB cluster parameter group.", "id": "describe-db-cluster-parameter-groups-cf9c6e66-664e-4f57-8e29-a9080abfc013", "title": "To list DB cluster parameter group settings"}], "DescribeDBClusterParameters": [{"input": {"DBClusterParameterGroupName": "mydbclusterparametergroup", "Source": "system"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists system parameters for the specified DB cluster parameter group.", "id": "describe-db-cluster-parameters-98043c28-e489-41a7-b118-bfd96dc779a1", "title": "To list DB cluster parameters"}], "DescribeDBClusterSnapshotAttributes": [{"input": {"DBClusterSnapshotIdentifier": "mydbclustersnapshot"}, "output": {"DBClusterSnapshotAttributesResult": {}}, "comments": {"input": {}, "output": {}}, "description": "This example lists attributes for the specified DB cluster snapshot.", "id": "describe-db-cluster-snapshot-attributes-6752ade3-0c7b-4b06-a8e4-b76bf4e2d3571", "title": "To list DB cluster snapshot attributes"}], "DescribeDBClusterSnapshots": [{"input": {"DBClusterSnapshotIdentifier": "mydbclustersnapshot", "SnapshotType": "manual"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists settings for the specified, manually-created cluster snapshot.", "id": "describe-db-cluster-snapshots-52f38af1-3431-4a51-9a6a-e6bb8c961b32", "title": "To list DB cluster snapshots"}], "DescribeDBClusters": [{"input": {"DBClusterIdentifier": "mynewdbcluster"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists settings for the specified DB cluster.", "id": "describe-db-clusters-7aae8861-cb95-4b3b-9042-f62df7698635", "title": "To list DB clusters"}], "DescribeDBEngineVersions": [{"input": {"DBParameterGroupFamily": "mysql5.6", "DefaultOnly": true, "Engine": "mysql", "EngineVersion": "5.6", "ListSupportedCharacterSets": true}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists settings for the specified DB engine version.", "id": "describe-db-engine-versions-8e698cf2-2162-425a-a854-111cdaceb52b", "title": "To list DB engine version settings"}], "DescribeDBInstances": [{"input": {"DBInstanceIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists settings for the specified DB instance.", "id": "describe-db-instances-0e11a8c5-4ec3-4463-8cbf-f7254d04c4fc", "title": "To list DB instance settings"}], "DescribeDBLogFiles": [{"input": {"DBInstanceIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FileLastWritten": 1470873600000, "FileSize": 0, "FilenameContains": "error"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists matching log file names for the specified DB instance, file name pattern, last write date in POSIX time with milleseconds, and minimum file size.", "id": "describe-db-log-files-5f002d8d-5c1d-44c2-b5f4-bd284c0f1285", "title": "To list DB log file names"}], "DescribeDBParameterGroups": [{"input": {"DBParameterGroupName": "mymysqlparametergroup"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists information about the specified DB parameter group.", "id": "describe-db-parameter-groups-", "title": "To list information about DB parameter groups"}], "DescribeDBParameters": [{"input": {"DBParameterGroupName": "mymysqlparametergroup", "MaxRecords": 20, "Source": "system"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists information for up to the first 20 system parameters for the specified DB parameter group.", "id": "describe-db-parameters-09db4201-ef4f-4d97-a4b5-d71c0715b901", "title": "To list information about DB parameters"}], "DescribeDBSecurityGroups": [{"input": {"DBSecurityGroupName": "mydbsecuritygroup"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists settings for the specified security group.", "id": "describe-db-security-groups-66fe9ea1-17dd-4275-b82e-f771cee0c849", "title": "To list DB security group settings"}], "DescribeDBSnapshotAttributes": [{"input": {"DBSnapshotIdentifier": "mydbsnapshot"}, "output": {"DBSnapshotAttributesResult": {}}, "comments": {"input": {}, "output": {}}, "description": "This example lists attributes for the specified DB snapshot.", "id": "describe-db-snapshot-attributes-1d4fb750-34f6-4e43-8b3d-b2751d796a95", "title": "To list DB snapshot attributes"}], "DescribeDBSnapshots": [{"input": {"DBInstanceIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IncludePublic": false, "IncludeShared": true, "SnapshotType": "manual"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists all manually-created, shared snapshots for the specified DB instance.", "id": "describe-db-snapshots-2c935989-a1ef-4c85-aea4-1d0f45f17f26", "title": "To list DB snapshot attributes"}], "DescribeDBSubnetGroups": [{"input": {"DBSubnetGroupName": "mydbsubnetgroup"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists information about the specified DB subnet group.", "id": "describe-db-subnet-groups-1d97b340-682f-4dd6-9653-8ed72a8d1221", "title": "To list information about DB subnet groups"}], "DescribeEngineDefaultClusterParameters": [{"input": {"DBParameterGroupFamily": "aurora5.6"}, "output": {"EngineDefaults": {}}, "comments": {"input": {}, "output": {}}, "description": "This example lists default parameters for the specified DB cluster engine.", "id": "describe-engine-default-cluster-parameters-f130374a-7bee-434b-b51d-da20b6e000e0", "title": "To list default parameters for a DB cluster engine"}], "DescribeEngineDefaultParameters": [{"input": {"DBParameterGroupFamily": "mysql5.6"}, "output": {"EngineDefaults": {}}, "comments": {"input": {}, "output": {}}, "description": "This example lists default parameters for the specified DB engine.", "id": "describe-engine-default-parameters-35d5108e-1d44-4fac-8aeb-04b8fdfface1", "title": "To list default parameters for a DB engine"}], "DescribeEventCategories": [{"input": {"SourceType": "db-instance"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists all DB instance event categories.", "id": "describe-event-categories-97bd4c77-12da-4be6-b42f-edf77771428b", "title": "To list event categories."}], "DescribeEventSubscriptions": [{"input": {"SubscriptionName": "mymysqleventsubscription"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists information for the specified DB event notification subscription.", "id": "describe-event-subscriptions-11184a82-e58a-4d0c-b558-f3a7489e0850", "title": "To list information about DB event notification subscriptions"}], "DescribeEvents": [{"input": {"Duration": 10080, "EventCategories": ["backup"], "SourceIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SourceType": "db-instance"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists information for all backup-related events for the specified DB instance for the past 7 days (7 days * 24 hours * 60 minutes = 10,080 minutes).", "id": "describe-events-3836e5ed-3913-4f76-8452-c77fcad5016b", "title": "To list information about events"}], "DescribeOptionGroupOptions": [{"input": {"EngineName": "mysql", "MajorEngineVersion": "5.6"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists information for all option group options for the specified DB engine.", "id": "describe-option-group-options-30d735a4-81f1-49e4-b3f2-5dc45d50c8ed", "title": "To list information about DB option group options"}], "DescribeOptionGroups": [{"input": {"EngineName": "mysql", "MajorEngineVersion": "5.6"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists information for all option groups for the specified DB engine.", "id": "describe-option-groups-4ef478a1-66d5-45f2-bec3-e608720418a4", "title": "To list information about DB option groups"}], "DescribeOrderableDBInstanceOptions": [{"input": {"DBInstanceClass": "db.t2.micro", "Engine": "mysql", "EngineVersion": "5.6.27", "LicenseModel": "general-public-license", "Vpc": true}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists information for all orderable DB instance options for the specified DB engine, engine version, DB instance class, license model, and VPC settings.", "id": "describe-orderable-db-instance-options-7444d3ed-82eb-42b9-9ed9-896b8c27a782", "title": "To list information about orderable DB instance options"}], "DescribePendingMaintenanceActions": [{"input": {"ResourceIdentifier": "arn:aws:rds:us-east-1:992648334831:db:my<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists information for all pending maintenance actions for the specified DB instance.", "id": "describe-pending-maintenance-actions-e6021f7e-58ae-49cc-b874-11996176835c", "title": "To list information about pending maintenance actions"}], "DescribeReservedDBInstances": [{"input": {"DBInstanceClass": "db.t2.micro", "Duration": "1y", "MultiAZ": false, "OfferingType": "No Upfront", "ProductDescription": "mysql"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists information for all reserved DB instances for the specified DB instance class, duration, product, offering type, and availability zone settings.", "id": "describe-reserved-db-instances-d45adaca-2e30-407c-a0f3-aa7b98bea17f", "title": "To list information about reserved DB instances"}], "DescribeReservedDBInstancesOfferings": [{"input": {"DBInstanceClass": "db.t2.micro", "Duration": "1y", "MultiAZ": false, "OfferingType": "No Upfront", "ProductDescription": "mysql"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists information for all reserved DB instance offerings for the specified DB instance class, duration, product, offering type, and availability zone settings.", "id": "describe-reserved-db-instances-offerings-9de7d1fd-d6a6-4a72-84ae-b2ef58d47d8d", "title": "To list information about reserved DB instance offerings"}], "DescribeSourceRegions": [{"input": {}, "output": {"SourceRegions": [{"Endpoint": "https://rds.ap-northeast-1.amazonaws.com", "RegionName": "ap-northeast-1", "Status": "available"}, {"Endpoint": "https://rds.ap-northeast-2.amazonaws.com", "RegionName": "ap-northeast-2", "Status": "available"}, {"Endpoint": "https://rds.ap-south-1.amazonaws.com", "RegionName": "ap-south-1", "Status": "available"}, {"Endpoint": "https://rds.ap-southeast-1.amazonaws.com", "RegionName": "ap-southeast-1", "Status": "available"}, {"Endpoint": "https://rds.ap-southeast-2.amazonaws.com", "RegionName": "ap-southeast-2", "Status": "available"}, {"Endpoint": "https://rds.eu-central-1.amazonaws.com", "RegionName": "eu-central-1", "Status": "available"}, {"Endpoint": "https://rds.eu-west-1.amazonaws.com", "RegionName": "eu-west-1", "Status": "available"}, {"Endpoint": "https://rds.sa-east-1.amazonaws.com", "RegionName": "sa-east-1", "Status": "available"}, {"Endpoint": "https://rds.us-west-1.amazonaws.com", "RegionName": "us-west-1", "Status": "available"}, {"Endpoint": "https://rds.us-west-2.amazonaws.com", "RegionName": "us-west-2", "Status": "available"}]}, "comments": {}, "description": "To list the AWS regions where a Read Replica can be created.", "id": "to-describe-source-regions-1473457722410", "title": "To describe source regions"}], "DownloadDBLogFilePortion": [{"input": {"DBInstanceIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LogFileName": "mysqlUpgrade"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists information for the specified log file for the specified DB instance.", "id": "download-db-log-file-portion-54a82731-a441-4fc7-a010-8eccae6fa202", "title": "To list information about DB log files"}], "FailoverDBCluster": [{"input": {"DBClusterIdentifier": "myaurorainstance-cluster", "TargetDBInstanceIdentifier": "myaurorareplica"}, "output": {"DBCluster": {}}, "comments": {"input": {}, "output": {}}, "description": "This example performs a failover for the specified DB cluster to the specified DB instance.", "id": "failover-db-cluster-9e7f2f93-d98c-42c7-bb0e-d6c485c096d6", "title": "To perform a failover for a DB cluster"}], "ListTagsForResource": [{"input": {"ResourceName": "arn:aws:rds:us-east-1:992648334831:og:mymysqloptiongroup"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example lists information about all tags associated with the specified DB option group.", "id": "list-tags-for-resource-8401f3c2-77cd-4f90-bfd5-b523f0adcc2f", "title": "To list information about tags associated with a resource"}], "ModifyDBCluster": [{"input": {"ApplyImmediately": true, "DBClusterIdentifier": "mydbcluster", "MasterUserPassword": "mynewpassword", "NewDBClusterIdentifier": "mynewdbcluster", "PreferredBackupWindow": "04:00-04:30", "PreferredMaintenanceWindow": "Tue:05:00-Tu<PERSON>:05:30"}, "output": {"DBCluster": {}}, "comments": {"input": {}, "output": {}}, "description": "This example changes the specified settings for the specified DB cluster.", "id": "modify-db-cluster-a370ee1b-768d-450a-853b-707cb1ab663d", "title": "To change DB cluster settings"}], "ModifyDBClusterParameterGroup": [{"input": {"DBClusterParameterGroupName": "mydbclusterparametergroup", "Parameters": [{"ApplyMethod": "immediate", "ParameterName": "time_zone", "ParameterValue": "America/Phoenix"}]}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example immediately changes the specified setting for the specified DB cluster parameter group.", "id": "modify-db-cluster-parameter-group-f9156bc9-082a-442e-8d12-239542c1a113", "title": "To change DB cluster parameter group settings"}], "ModifyDBClusterSnapshotAttribute": [{"input": {"AttributeName": "restore", "DBClusterSnapshotIdentifier": "manual-cluster-snapshot1", "ValuesToAdd": ["************", "************"], "ValuesToRemove": ["all"]}, "output": {"DBClusterSnapshotAttributesResult": {}}, "comments": {"input": {}, "output": {}}, "description": "The following example gives two AWS accounts access to a manual DB cluster snapshot and ensures that the DB cluster snapshot is private by removing the value \"all\".", "id": "to-add-or-remove-access-to-a-manual-db-cluster-snapshot-*************", "title": "To add or remove access to a manual DB cluster snapshot"}], "ModifyDBInstance": [{"input": {"AllocatedStorage": 10, "ApplyImmediately": true, "BackupRetentionPeriod": 1, "DBInstanceClass": "db.t2.small", "DBInstanceIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MasterUserPassword": "mynewpassword", "PreferredBackupWindow": "04:00-04:30", "PreferredMaintenanceWindow": "Tue:05:00-Tu<PERSON>:05:30"}, "output": {"DBInstance": {}}, "comments": {"input": {}, "output": {}}, "description": "This example immediately changes the specified settings for the specified DB instance.", "id": "modify-db-instance-6979a368-6254-467b-8a8d-61103f4fcde9", "title": "To change DB instance settings"}], "ModifyDBParameterGroup": [{"input": {"DBParameterGroupName": "mymysqlparametergroup", "Parameters": [{"ApplyMethod": "immediate", "ParameterName": "time_zone", "ParameterValue": "America/Phoenix"}]}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example immediately changes the specified setting for the specified DB parameter group.", "id": "modify-db-parameter-group-f3a4e52a-68e4-4b88-b559-f912d34c457a", "title": "To change DB parameter group settings"}], "ModifyDBSnapshotAttribute": [{"input": {"AttributeName": "restore", "DBSnapshotIdentifier": "mydbsnapshot", "ValuesToAdd": ["all"]}, "output": {"DBSnapshotAttributesResult": {}}, "comments": {"input": {}, "output": {}}, "description": "This example adds the specified attribute for the specified DB snapshot.", "id": "modify-db-snapshot-attribute-2e66f120-2b21-4a7c-890b-4474da88bde6", "title": "To change DB snapshot attributes"}], "ModifyDBSubnetGroup": [{"input": {"DBSubnetGroupName": "mydbsubnetgroup", "SubnetIds": ["subnet-70e1975a", "subnet-747a5c49"]}, "output": {"DBSubnetGroup": {}}, "comments": {"input": {}, "output": {}}, "description": "This example changes the specified setting for the specified DB subnet group.", "id": "modify-db-subnet-group-e34a97d9-8fe6-4239-a4ed-ad6e73a956b0", "title": "To change DB subnet group settings"}], "ModifyEventSubscription": [{"input": {"Enabled": true, "EventCategories": ["deletion", "low storage"], "SourceType": "db-instance", "SubscriptionName": "mymysqleventsubscription"}, "output": {"EventSubscription": {}}, "comments": {"input": {}, "output": {}}, "description": "This example changes the specified setting for the specified event notification subscription.", "id": "modify-event-subscription-405ac869-1f02-42cd-b8f4-6950a435f30e", "title": "To change event notification subscription settings"}], "ModifyOptionGroup": [{"input": {"ApplyImmediately": true, "OptionGroupName": "myawsuser-og02", "OptionsToInclude": [{"DBSecurityGroupMemberships": ["default"], "OptionName": "MEMCACHED"}]}, "output": {"OptionGroup": {}}, "comments": {"input": {}, "output": {}}, "description": "The following example adds an option to an option group.", "id": "to-modify-an-option-group-1473890247875", "title": "To modify an option group"}], "PromoteReadReplica": [{"input": {"BackupRetentionPeriod": 1, "DBInstanceIdentifier": "mydbreadreplica", "PreferredBackupWindow": "03:30-04:00"}, "output": {"DBInstance": {}}, "comments": {"input": {}, "output": {}}, "description": "This example promotes the specified read replica and sets its backup retention period and preferred backup window.", "id": "promote-read-replica-cc580039-c55d-4035-838a-def4a1ae4181", "title": "To promote a read replica"}], "PurchaseReservedDBInstancesOffering": [{"input": {"ReservedDBInstanceId": "myreservationid", "ReservedDBInstancesOfferingId": "fb29428a-646d-4390-850e-5fe89926e727"}, "output": {"ReservedDBInstance": {}}, "comments": {"input": {}, "output": {}}, "description": "This example purchases a reserved DB instance offering that matches the specified settings.", "id": "purchase-reserved-db-instances-offfering-f423c736-8413-429b-ba13-850fd4fa4dcd", "title": "To purchase a reserved DB instance offering"}], "RebootDBInstance": [{"input": {"DBInstanceIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ForceFailover": false}, "output": {"DBInstance": {}}, "comments": {"input": {}, "output": {}}, "description": "This example reboots the specified DB instance without forcing a failover.", "id": "reboot-db-instance-b9ce8a0a-2920-451d-a1f3-01d288aa7366", "title": "To reboot a DB instance"}], "RemoveSourceIdentifierFromSubscription": [{"input": {"SourceIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SubscriptionName": "myeventsubscription"}, "output": {"EventSubscription": {}}, "comments": {"input": {}, "output": {}}, "description": "This example removes the specified source identifier from the specified DB event subscription.", "id": "remove-source-identifier-from-subscription-30d25493-c19d-4cf7-b4e5-68371d0d8770", "title": "To remove a source identifier from a DB event subscription"}], "RemoveTagsFromResource": [{"input": {"ResourceName": "arn:aws:rds:us-east-1:992648334831:og:mydboptiongroup", "TagKeys": ["<PERSON><PERSON><PERSON>"]}, "comments": {"input": {}, "output": {}}, "description": "This example removes the specified tag associated with the specified DB option group.", "id": "remove-tags-from-resource-49f00574-38f6-4d01-ac89-d3c668449ce3", "title": "To remove tags from a resource"}], "ResetDBClusterParameterGroup": [{"input": {"DBClusterParameterGroupName": "mydbclusterparametergroup", "ResetAllParameters": true}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example resets all parameters for the specified DB cluster parameter group to their default values.", "id": "reset-db-cluster-parameter-group-b04aeaf7-7f73-49e1-9bb4-857573ea3ee4", "title": "To reset the values of a DB cluster parameter group"}], "ResetDBParameterGroup": [{"input": {"DBParameterGroupName": "mydbparametergroup", "ResetAllParameters": true}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example resets all parameters for the specified DB parameter group to their default values.", "id": "reset-db-parameter-group-ed2ed723-de0d-4824-8af5-3c65fa130abf", "title": "To reset the values of a DB parameter group"}], "RestoreDBClusterFromSnapshot": [{"input": {"DBClusterIdentifier": "restored-cluster1", "Engine": "aurora", "SnapshotIdentifier": "sample-cluster-snapshot1"}, "output": {"DBCluster": {}}, "comments": {"input": {}, "output": {}}, "description": "The following example restores an Amazon Aurora DB cluster from a DB cluster snapshot.", "id": "to-restore-an-amazon-aurora-db-cluster-from-a-db-cluster-snapshot-1473958144325", "title": "To restore an Amazon Aurora DB cluster from a DB cluster snapshot"}], "RestoreDBClusterToPointInTime": [{"input": {"DBClusterIdentifier": "sample-restored-cluster1", "RestoreToTime": "2016-09-13T18:45:00Z", "SourceDBClusterIdentifier": "sample-cluster1"}, "output": {"DBCluster": {}}, "comments": {"input": {}, "output": {}}, "description": "The following example restores a DB cluster to a new DB cluster at a point in time from the source DB cluster.", "id": "to-restore-a-db-cluster-to-a-point-in-time-1473962082214", "title": "To restore a DB cluster to a point in time."}], "RestoreDBInstanceFromDBSnapshot": [{"input": {"DBInstanceIdentifier": "mysqldb-restored", "DBSnapshotIdentifier": "rds:mysqldb-2014-04-22-08-15"}, "output": {"DBInstance": {"AllocatedStorage": 200, "AutoMinorVersionUpgrade": true, "AvailabilityZone": "us-west-2b", "BackupRetentionPeriod": 7, "CACertificateIdentifier": "rds-ca-2015", "CopyTagsToSnapshot": false, "DBInstanceArn": "arn:aws:rds:us-west-2:************:db:mysqldb-restored", "DBInstanceClass": "db.t2.small", "DBInstanceIdentifier": "mysqldb-restored", "DBInstanceStatus": "available", "DBName": "sample", "DBParameterGroups": [{"DBParameterGroupName": "default.mysql5.6", "ParameterApplyStatus": "in-sync"}], "DBSecurityGroups": [], "DBSubnetGroup": {"DBSubnetGroupDescription": "default", "DBSubnetGroupName": "default", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetAvailabilityZone": {"Name": "us-west-2a"}, "SubnetIdentifier": "subnet-77e8db03", "SubnetStatus": "Active"}, {"SubnetAvailabilityZone": {"Name": "us-west-2b"}, "SubnetIdentifier": "subnet-c39989a1", "SubnetStatus": "Active"}, {"SubnetAvailabilityZone": {"Name": "us-west-2c"}, "SubnetIdentifier": "subnet-4b267b0d", "SubnetStatus": "Active"}], "VpcId": "vpc-c1c5b3a3"}, "DbInstancePort": 0, "DbiResourceId": "db-VNZUCCBTEDC4WR7THXNJO72HVQ", "DomainMemberships": [], "Engine": "mysql", "EngineVersion": "5.6.27", "LicenseModel": "general-public-license", "MasterUsername": "my<PERSON><PERSON>", "MonitoringInterval": 0, "MultiAZ": false, "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "PendingModifiedValues": {}, "PreferredBackupWindow": "12:58-13:28", "PreferredMaintenanceWindow": "tue:10:16-tue:10:46", "PubliclyAccessible": true, "ReadReplicaDBInstanceIdentifiers": [], "StorageEncrypted": false, "StorageType": "gp2", "VpcSecurityGroups": [{"Status": "active", "VpcSecurityGroupId": "sg-e5e5b0d2"}]}}, "comments": {"input": {}, "output": {}}, "description": "The following example restores a DB instance from a DB snapshot.", "id": "to-restore-a-db-instance-from-a-db-snapshot-1473961657311", "title": "To restore a DB instance from a DB snapshot."}], "RestoreDBInstanceToPointInTime": [{"input": {"RestoreTime": "2016-09-13T18:45:00Z", "SourceDBInstanceIdentifier": "mysql-sample", "TargetDBInstanceIdentifier": "mysql-sample-restored"}, "output": {"DBInstance": {"AllocatedStorage": 200, "AutoMinorVersionUpgrade": true, "AvailabilityZone": "us-west-2b", "BackupRetentionPeriod": 7, "CACertificateIdentifier": "rds-ca-2015", "CopyTagsToSnapshot": false, "DBInstanceArn": "arn:aws:rds:us-west-2:************:db:mysql-sample-restored", "DBInstanceClass": "db.t2.small", "DBInstanceIdentifier": "mysql-sample-restored", "DBInstanceStatus": "available", "DBName": "sample", "DBParameterGroups": [{"DBParameterGroupName": "default.mysql5.6", "ParameterApplyStatus": "in-sync"}], "DBSecurityGroups": [], "DBSubnetGroup": {"DBSubnetGroupDescription": "default", "DBSubnetGroupName": "default", "SubnetGroupStatus": "Complete", "Subnets": [{"SubnetAvailabilityZone": {"Name": "us-west-2a"}, "SubnetIdentifier": "subnet-77e8db03", "SubnetStatus": "Active"}, {"SubnetAvailabilityZone": {"Name": "us-west-2b"}, "SubnetIdentifier": "subnet-c39989a1", "SubnetStatus": "Active"}, {"SubnetAvailabilityZone": {"Name": "us-west-2c"}, "SubnetIdentifier": "subnet-4b267b0d", "SubnetStatus": "Active"}], "VpcId": "vpc-c1c5b3a3"}, "DbInstancePort": 0, "DbiResourceId": "db-VNZUCCBTEDC4WR7THXNJO72HVQ", "DomainMemberships": [], "Engine": "mysql", "EngineVersion": "5.6.27", "LicenseModel": "general-public-license", "MasterUsername": "my<PERSON><PERSON>", "MonitoringInterval": 0, "MultiAZ": false, "OptionGroupMemberships": [{"OptionGroupName": "default:mysql-5-6", "Status": "in-sync"}], "PendingModifiedValues": {}, "PreferredBackupWindow": "12:58-13:28", "PreferredMaintenanceWindow": "tue:10:16-tue:10:46", "PubliclyAccessible": true, "ReadReplicaDBInstanceIdentifiers": [], "StorageEncrypted": false, "StorageType": "gp2", "VpcSecurityGroups": [{"Status": "active", "VpcSecurityGroupId": "sg-e5e5b0d2"}]}}, "comments": {"input": {}, "output": {}}, "description": "The following example restores a DB instance to a new DB instance at a point in time from the source DB instance.", "id": "to-restore-a-db-instance-to-a-point-in-time-1473962652154", "title": "To restore a DB instance to a point in time."}], "RevokeDBSecurityGroupIngress": [{"input": {"CIDRIP": "***********/32", "DBSecurityGroupName": "mydbsecuritygroup"}, "output": {"DBSecurityGroup": {}}, "comments": {"input": {}, "output": {}}, "description": "This example revokes ingress for the specified CIDR block associated with the specified DB security group.", "id": "revoke-db-security-group-ingress-ce5b2c1c-bd4e-4809-b04a-6d78ec448813", "title": "To revoke ingress for a DB security group"}]}}