{"pagination": {"ListApplications": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ApplicationSummaryList"}, "ListEnvironmentVpcs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "EnvironmentVpcList"}, "ListEnvironments": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "EnvironmentSummaryList"}, "ListRoutes": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "RouteSummaryList"}, "ListServices": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ServiceSummaryList"}}}