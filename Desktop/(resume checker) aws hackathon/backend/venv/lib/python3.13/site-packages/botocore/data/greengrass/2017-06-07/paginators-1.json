{"pagination": {"ListBulkDeploymentDetailedReports": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Deployments"}, "ListBulkDeployments": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "BulkDeployments"}, "ListConnectorDefinitionVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Versions"}, "ListConnectorDefinitions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Definitions"}, "ListCoreDefinitionVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Versions"}, "ListCoreDefinitions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Definitions"}, "ListDeployments": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Deployments"}, "ListDeviceDefinitionVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Versions"}, "ListDeviceDefinitions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Definitions"}, "ListFunctionDefinitionVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Versions"}, "ListFunctionDefinitions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Definitions"}, "ListGroupVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Versions"}, "ListGroups": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Groups"}, "ListLoggerDefinitionVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Versions"}, "ListLoggerDefinitions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Definitions"}, "ListResourceDefinitionVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Versions"}, "ListResourceDefinitions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Definitions"}, "ListSubscriptionDefinitionVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Versions"}, "ListSubscriptionDefinitions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Definitions"}}}