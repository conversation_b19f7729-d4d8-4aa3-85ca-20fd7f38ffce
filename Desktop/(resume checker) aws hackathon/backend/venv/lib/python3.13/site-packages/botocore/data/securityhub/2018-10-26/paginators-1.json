{"pagination": {"GetEnabledStandards": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "StandardsSubscriptions"}, "GetFindings": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Findings"}, "GetInsights": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Insights"}, "ListEnabledProductsForImport": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ProductSubscriptions"}, "ListInvitations": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Invitations"}, "ListMembers": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Members"}, "DescribeActionTargets": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ActionTargets"}, "DescribeProducts": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Products"}, "DescribeStandards": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Standards"}, "DescribeStandardsControls": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Controls"}, "ListOrganizationAdminAccounts": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AdminAccounts"}, "ListFindingAggregators": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "FindingAggregators"}, "ListSecurityControlDefinitions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "SecurityControlDefinitions"}, "ListStandardsControlAssociations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "StandardsControlAssociationSummaries"}, "GetFindingHistory": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Records"}, "ListConfigurationPolicies": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ConfigurationPolicySummaries"}, "ListConfigurationPolicyAssociations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ConfigurationPolicyAssociationSummaries"}}}