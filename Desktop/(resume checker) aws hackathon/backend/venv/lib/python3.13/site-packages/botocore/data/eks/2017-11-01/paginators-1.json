{"pagination": {"ListClusters": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "clusters"}, "ListUpdates": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "updateIds"}, "ListNodegroups": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "nodegroups"}, "ListFargateProfiles": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "fargateProfileNames"}, "DescribeAddonVersions": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "addons"}, "ListAddons": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "addons"}, "ListIdentityProviderConfigs": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "identityProviderConfigs"}, "ListEksAnywhereSubscriptions": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "subscriptions"}, "ListPodIdentityAssociations": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "associations"}}}