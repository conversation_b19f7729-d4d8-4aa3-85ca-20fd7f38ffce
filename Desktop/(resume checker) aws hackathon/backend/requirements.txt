# AWS SDK
boto3>=1.34.0
botocore>=1.34.0

# HTTP requests for Gemini API
requests==2.31.0

# PDF processing
PyPDF2==3.0.1

# DOCX processing (optional)
python-docx==1.1.0

# JSON handling (built-in, but explicit for clarity)
# json - built-in

# Logging (built-in)
# logging - built-in

# Regular expressions (built-in)
# re - built-in

# Type hints (built-in in Python 3.12)
# typing - built-in

# UUID generation (built-in)
# uuid - built-in

# Date/time handling (built-in)
# datetime - built-in

# Base64 encoding (built-in)
# base64 - built-in

# IO operations (built-in)
# io - built-in

# OS environment variables (built-in)
# os - built-in
