#!/usr/bin/env python3
"""
Test script to verify all imports work correctly
"""

def test_imports():
    """Test all required imports"""
    try:
        print("Testing imports...")
        
        # Test standard library imports
        import zipfile
        import os
        import json
        print("✅ Standard library imports successful")
        
        # Test AWS SDK imports
        import boto3
        print("✅ boto3 import successful")
        
        from botocore.exceptions import ClientError
        print("✅ botocore.exceptions import successful")
        
        # Test that we can create clients
        lambda_client = boto3.client('lambda', region_name='us-east-2')
        print("✅ Lambda client creation successful")
        
        print("\n🎉 All imports and basic functionality working!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_imports()
    exit(0 if success else 1)
